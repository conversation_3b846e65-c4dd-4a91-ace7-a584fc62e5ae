import { updateSession } from "./supabase/client/middleware";
import { type NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  const { response, supabase } = await updateSession(request, NextResponse.next());
  const nextUrl = request.nextUrl;

  // Add debug logging to see if middleware is running
  console.log(`Middleware running for: ${nextUrl.pathname}`);

  const {
    data: { session },
  } = await supabase.auth.getSession();

  console.log(`Session exists: ${!!session}`);

  // Allow access to sign-in page, auth callback, and API routes without authentication
  const publicPaths = ["/sign-in", "/auth", "/api"];
  const isPublicPath = publicPaths.some(path => nextUrl.pathname.startsWith(path));

  console.log(`Is public path: ${isPublicPath}`);

  // Not authenticated and trying to access protected route
  if (!session && !isPublicPath) {
    console.log(`Redirecting unauthenticated user from ${nextUrl.pathname} to /sign-in`);

    const encodedSearchParams = `${nextUrl.pathname.substring(1)}${
      nextUrl.search
    }`;

    const url = new URL("/sign-in", request.url);

    if (encodedSearchParams) {
      url.searchParams.append("return_to", encodedSearchParams);
    }

    return NextResponse.redirect(url);
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - Files with extensions (e.g., .png, .jpg, .css, .js)
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\..*).*)",
  ],
};
