import { updateSession } from "./supabase/client/middleware";
import { createClient } from "./supabase/client/server";
import { type NextRequest, NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  const response = await updateSession(request, NextResponse.next());
  const supabase = await createClient();
  const nextUrl = request.nextUrl;

  const {
    data: { session },
  } = await supabase.auth.getSession();

  // Allow access to sign-in page, auth callback, and API routes without authentication
  const publicPaths = ["/sign-in", "/auth", "/api"];
  const isPublicPath = publicPaths.some(path => nextUrl.pathname.startsWith(path));

  // Not authenticated and trying to access protected route
  if (!session && !isPublicPath) {
    const encodedSearchParams = `${nextUrl.pathname.substring(1)}${
      nextUrl.search
    }`;

    const url = new URL("/sign-in", request.url);

    if (encodedSearchParams) {
      url.searchParams.append("return_to", encodedSearchParams);
    }

    return NextResponse.redirect(url);
  }

  return response;
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico|.*\\..*).*)"],
};
