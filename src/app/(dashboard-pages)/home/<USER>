import { createClient } from "@/supabase/client/server";
import { redirect } from "next/navigation";
import HomePageClient from "./home-client";

export default async function HomePage() {
  // Server-side authentication check
  const supabase = await createClient();
  const { data: { session }, error } = await supabase.auth.getSession();

  console.log(`🏠 Home page - Session exists: ${!!session}`);

  if (error) {
    console.error(`❌ Home page session error:`, error);
  }

  if (!session) {
    console.log(`🚫 No session found, redirecting to sign-in`);
    redirect('/sign-in');
  }

  // If authenticated, render the client component
  return <HomePageClient />;
}
