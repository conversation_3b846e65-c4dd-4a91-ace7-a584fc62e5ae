"use server";

import { encodedRedirect } from "@/utils/utils";
import { createClient } from "@/supabase/client/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { z } from 'zod';

export const signOutAction = async () => {
  const supabase = await createClient();
  await supabase.auth.signOut();
  return redirect("/sign-in");
};

export const signInWithGoogleAction = async () => {
  const supabase = await createClient();
  const origin = (await headers()).get("origin");

  // For production environments, use the NEXT_PUBLIC_SITE_URL environment variable if available
  const redirectUrl = process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_URL || origin;

  if (!redirectUrl) {
    console.error("Unable to determine redirect URL.");
    return encodedRedirect(
      "error",
      "/sign-in",
      "Could not determine the application origin. Unable to proceed with Google Sign-In.",
    );
  }

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: "google",
    options: {
      redirectTo: `${redirectUrl}/auth/callback`,
    },
  });

  if (error) {
    console.error("Google Sign-In error:", error?.message ?? "Unknown error");
    return encodedRedirect("error", "/sign-in", error?.message ?? "Could not authenticate with Google.");
  }

  if (data.url) {
    return redirect(data.url);
  } else {
    console.error("Google Sign-In did not return a URL.");
    return encodedRedirect(
      "error",
      "/sign-in",
      "Could not initiate Google Sign-In.",
    );
  }
};

// Define types for action state
export type WaitlistFormState = {
  type: "success" | "error" | "idle";
  message: string | null;
};

const WaitlistEmailSchema = z.object({
  email: z.string().email({ message: "Invalid email address." }),
});

export const joinWaitlistAction = async (
  prevState: WaitlistFormState,
  formData: FormData
): Promise<WaitlistFormState> => { // Return state object
  const rawFormData = {
    email: formData.get("email")?.toString(),
  };

  const validatedFields = WaitlistEmailSchema.safeParse(rawFormData);

  if (!validatedFields.success) {
    return {
      type: "error",
      message: validatedFields.error.flatten().fieldErrors.email?.[0] ?? "Validation failed.",
    };
  }

  const { email } = validatedFields.data;
  const supabase = await createClient();

  const { error } = await supabase
    .from('waitlist')
    .insert([{ email: email }]);

  if (error) {
    console.error("Waitlist Insert Error:", error?.message ?? "Unknown error");
    if (error.code === '23505') {
      return {
        type: "error",
        message: "This email is already on the waitlist.",
      };
    }
    return {
      type: "error",
      message: error?.message ?? "Could not add email to the waitlist. Please try again.",
    };
  }

  // Success!
  return {
    type: "success",
    message: "Success! You've been added to the waitlist.",
  };
};

export const resetPasswordAction = async (formData: FormData) => {
  const password = formData.get("password")?.toString();
  const confirmPassword = formData.get("confirmPassword")?.toString();

  if (!password || !confirmPassword) {
    return encodedRedirect("error", "/protected/reset-password", "Please provide both password fields.");
  }

  if (password !== confirmPassword) {
    return encodedRedirect("error", "/protected/reset-password", "Passwords do not match.");
  }

  try {
    const supabase = await createClient();
    const { error } = await supabase.auth.updateUser({ password });

    if (error) {
      console.error("Password reset error:", error.message);
      return encodedRedirect("error", "/protected/reset-password", error.message);
    }

    return encodedRedirect("success", "/protected", "Your password has been reset successfully.");
  } catch (err) {
    console.error("Password reset exception:", err);
    return encodedRedirect("error", "/protected/reset-password", "An unexpected error occurred.");
  }
};
