import { createClient } from "@/supabase/client/server";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  // The `/auth/callback` route is required for the server-side auth flow implemented
  // by the SSR package. It exchanges an auth code for the user's session.
  // https://supabase.com/docs/guides/auth/server-side/nextjs
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");

  // Use the NEXT_PUBLIC_SITE_URL environment variable if available, otherwise fall back to request origin
  const origin = process.env.NEXT_PUBLIC_SITE_URL || requestUrl.origin;

  const redirectTo = requestUrl.searchParams.get("return_to")?.toString();

  const supabase = await createClient();

  if (code) {
    await supabase.auth.exchangeCodeForSession(code);
  }

  // Get the user after authentication
  const { data: { user } } = await supabase.auth.getUser();

  // Log successful authentication
  if (user && user.email) {
    console.log(`AUTH CALLBACK: User ${user.email} successfully authenticated`);
  }

  if (redirectTo) {
    // Ensure the redirect path starts with / and is safe
    const safePath = redirectTo.startsWith('/') ? redirectTo : `/${redirectTo}`;
    return NextResponse.redirect(`${origin}${safePath}`);
  }

  // URL to redirect to after sign in process completes
  return NextResponse.redirect(`${origin}/home`);
}
